# Cloudflare API Integration Documentation

## New GetZonesWithAPI Method

The `GetZonesWithAPI` method has been added to the Cloudflare service to provide direct API access to Cloudflare zones data, complementing the existing Terraform-based approach.

### API Endpoint

```
GET /api/v1/cloudflare/zones/api
```

### Query Parameters

| Parameter | Type | Default | Max | Description |
|-----------|------|---------|-----|-------------|
| `page` | int | 1 | - | Page number for pagination |
| `per_page` | int | 20 | 50 | Number of results per page |
| `name` | string | - | - | Domain name to filter zones (optional) |
| `account.name` | string | - | - | Account name to filter zones (optional) |

### Example Requests

#### Get all zones (default pagination)
```bash
curl -X GET "http://localhost:8080/api/v1/cloudflare/zones/api" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Get zones with pagination
```bash
curl -X GET "http://localhost:8080/api/v1/cloudflare/zones/api?page=2&per_page=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Filter zones by domain name
```bash
curl -X GET "http://localhost:8080/api/v1/cloudflare/zones/api?name=example.com" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Filter zones by account name
```bash
curl -X GET "http://localhost:8080/api/v1/cloudflare/zones/api?account.name=MyAccount" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Response Format

The API returns a structured response matching the Cloudflare API format:

```json
{
  "status": true,
  "message": "Zones retrieved successfully from API",
  "data": {
    "result": [
      {
        "id": "zone_id_here",
        "name": "example.com",
        "status": "active",
        "paused": false,
        "type": "full",
        "development_mode": 0,
        "name_servers": [
          "ns1.cloudflare.com",
          "ns2.cloudflare.com"
        ],
        "original_name_servers": [
          "ns1.example.com",
          "ns2.example.com"
        ],
        "original_registrar": "Example Registrar",
        "original_dnshost": "Example DNS Host",
        "modified_on": "2023-01-01T00:00:00Z",
        "created_on": "2023-01-01T00:00:00Z",
        "activated_on": "2023-01-01T00:00:00Z",
        "meta": {
          "step": 4,
          "custom_certificate_quota": 1,
          "page_rule_quota": 3,
          "phishing_detected": false,
          "multiple_railguns_allowed": false
        },
        "owner": {
          "id": "owner_id",
          "type": "user",
          "email": "<EMAIL>"
        },
        "account": {
          "id": "account_id",
          "name": "Account Name"
        },
        "permissions": ["#zone:read", "#zone:edit"],
        "plan": {
          "id": "plan_id",
          "name": "Free Website",
          "price": 0,
          "currency": "USD",
          "frequency": "monthly",
          "is_subscribed": true,
          "can_subscribe": false,
          "legacy_id": "free",
          "legacy_discount": false,
          "externally_managed": false
        }
      }
    ],
    "result_info": {
      "page": 1,
      "per_page": 20,
      "total_pages": 1,
      "count": 1,
      "total_count": 1
    },
    "success": true,
    "errors": [],
    "messages": []
  }
}
```

### Environment Variables

Make sure the following environment variable is set:

- `CLOUDFLARE_API_TOKEN`: Your Cloudflare API token with appropriate permissions

### Error Handling

The method includes comprehensive error handling for:
- Missing API token
- Network failures
- HTTP errors
- JSON parsing errors
- Cloudflare API errors

### Implementation Details

#### Service Layer
- **File**: `internal/core/services/cloudflare.go`
- **Method**: `GetZonesWithAPI(page int, name string, perPage int, accountName string) (*dto.CloudflareAPIZonesResponse, error)`

#### Handler Layer
- **File**: `internal/adapters/http/handlers/cloudflare_handler.go`
- **Method**: `GetZonesWithAPI(c *fiber.Ctx) error`

#### DTOs
- **File**: `internal/core/dto/cloudflare.go`
- **Types**: `CloudflareAPIZonesResponse`, `CloudflareAPIZone`, `CloudflareAPIResultInfo`, etc.

#### Routes
- **File**: `internal/adapters/http/routes/cloudflare_routes.go`
- **Route**: `GET /zones/api`

### Testing

A test file `test_cloudflare_api.go` has been created to verify the implementation. To run it:

1. Set your Cloudflare API token:
   ```bash
   export CLOUDFLARE_API_TOKEN="your_token_here"
   ```

2. Run the test:
   ```bash
   go run test_cloudflare_api.go
   ```

### Logging

The implementation includes detailed logging for:
- API request URLs
- Response status codes
- Success/failure messages
- Zone counts retrieved

This helps with debugging and monitoring API usage.
